import { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface ImageUploadZoneProps {
  onFileSelect: (file: File) => void;
  previewUrl?: string | null;
  disabled?: boolean;
}

export default function ImageUploadZone({ 
  onFileSelect, 
  previewUrl, 
  disabled = false 
}: ImageUploadZoneProps) {
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (disabled) return;
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (disabled) return;
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      onFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;
    if (e.target.files && e.target.files[0]) {
      onFileSelect(e.target.files[0]);
    }
  };

  return (
    <motion.div
      className={`relative border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 ${
        disabled 
          ? 'border-gray-600 bg-gray-800/20 cursor-not-allowed' 
          : dragActive 
            ? 'border-purple-400 bg-purple-500/20' 
            : 'border-gray-400/50 hover:border-purple-400/70 hover:bg-purple-500/10 cursor-pointer'
      }`}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
      whileHover={disabled ? {} : { scale: 1.02 }}
      whileTap={disabled ? {} : { scale: 0.98 }}
    >
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInput}
        disabled={disabled}
        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
        aria-label="Upload image"
      />
      
      <AnimatePresence mode="wait">
        {previewUrl ? (
          <motion.div
            key="preview"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="space-y-4"
          >
            <img 
              src={previewUrl} 
              alt="Preview" 
              className="max-w-full max-h-64 mx-auto rounded-xl shadow-lg"
            />
            <p className="text-green-400 font-medium">Image ready for processing!</p>
          </motion.div>
        ) : (
          <motion.div
            key="upload"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            <motion.div
              className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${
                disabled 
                  ? 'bg-gray-600' 
                  : 'bg-gradient-to-r from-purple-500 to-pink-500'
              }`}
              animate={{ 
                rotate: dragActive ? 180 : 0,
                scale: dragActive ? 1.1 : 1
              }}
              transition={{ duration: 0.3 }}
            >
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </motion.div>
            <div>
              <p className={`text-xl font-semibold mb-2 ${
                disabled ? 'text-gray-500' : 'text-white'
              }`}>
                {disabled 
                  ? "Upload disabled" 
                  : dragActive 
                    ? "Drop your image here!" 
                    : "Drag & drop your image"
                }
              </p>
              {!disabled && (
                <p className="text-gray-400">
                  or <span className="text-purple-400 font-medium">click to browse</span>
                </p>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
